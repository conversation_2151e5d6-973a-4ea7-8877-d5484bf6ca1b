/* eslint-disable no-unused-vars */
import { Timestamp } from "firebase-admin/firestore";

export enum CollectionStatus {
  PREMARKET = "PREMARKET",
  MARKET = "MARKET",
  DELETED = "DELETED",
}

export enum UserType {
  BUYER = "buyer",
  SELLER = "seller",
}

export interface CollectionEntity {
  id: string;
  name: string;
  description: string;
  status: CollectionStatus;
  launchedAt?: Timestamp;
  floorPrice: number; // Minimum floor price for collection items in TON
  active: boolean; // Whether the collection is active for order creation
  lock_period?: number; // Lock period in days for this collection (overrides app config)
}

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role: "user" | "admin";
  tg_id?: string;
  telegram_handle?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string; // Telegram ID of the user who referred this user
  referral_fee?: number; // Custom referral fee in BPS (basis points)
  points?: number; // Points earned from referrals
  balance?: UserBalance;
}

export enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid",
  GIFT_SENT_TO_RELAYER = "gift_sent_to_relayer",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderFees {
  buyer_locked_percentage: number; // in BPS
  seller_locked_percentage: number; // in BPS
  purchase_fee: number; // in BPS
  referrer_fee: number; // in BPS
  order_cancellation_fee: number; // in BPS
  resell_purchase_fee: number; // in BPS
  resell_purchase_fee_for_seller: number; // in BPS
}

export interface OrderEntity {
  id: string;
  number: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId?: string; // Optional since orders can be created without a seller
  collectionId: string; // Collection ID for floor price validation
  price: number;
  status: OrderStatus;
  deadline?: Timestamp; // Deadline for seller to fulfill the order
  giftSentToRelayerAt?: Timestamp; // When gift was sent to relayer
  owned_gift_id: string | null; // ID of the gift owned by the user creating the order
  secondaryMarketPrice?: number | null; // Price set by buyer for reselling on secondary market
  reseller_earnings_for_seller?: number; // Accumulated earnings for seller from resells
  fees: OrderFees; // Fees snapshot from app_config at order creation time
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ResellTxHistoryEntity {
  id?: string;
  order_id: string;
  execution_price: string; // TON amount as string
  reseller_id: string; // user who resold the order
  executed_at: Timestamp; // timestamp of resell transaction
  buyer_id: string; // user who bought the resold order
}

export enum UserRole {
  BUYER = "buyer",
  SELLER = "seller",
}

export enum TxType {
  DEPOSIT = "deposit",
  WITHDRAW = "withdraw",
  BUY_LOCK_COLLATERAL = "buy_lock_collateral",
  UNLOCK_COLLATERAL = "unlock_collateral",
  SELL_LOCK_COLLATERAL = "sell_lock_collateral",
  REFERRAL_FEE = "referral_fee",
  CANCELATION_FEE = "cancelation_fee", //add in code
  REFUND = "refund",
  SELL_FULFILLMENT = "sell_fulfillment", //add in code
  RESELL_FEE_EARNINGS = "resell_fee_earnings", //add in code
}

export interface UserTxEntity {
  id?: string;
  tx_type: TxType;
  user_id: string;
  amount: number; // can be positive or negative
  order_id?: string; // optional reference to related order
  description?: string; // optional description for the transaction
  createdAt: Timestamp;
}

export interface TxLookupEntity {
  id: string;
  last_checked_record_id: string;
  updatedAt: Timestamp;
}

export interface AppConfigEntity {
  deposit_fee: number; // Static TON value
  withdrawal_fee: number; // Static TON value
  referrer_fee: number; // in BPS (basis points)
  cancel_order_fee: number; // in BPS - dynamic fee for two-person order cancellations
  purchase_fee: number; // in BPS
  buyer_lock_percentage: number; // in BPS (basis points)
  seller_lock_percentage: number; // in BPS (basis points)
  resell_purchase_fee: number; // in BPS - fee applied to secondary market purchases
  resell_purchase_fee_for_seller: number; // in BPS - fee for seller on each resell
  min_deposit_amount: number; // Static TON value
  min_withdrawal_amount: number; // Static TON value
  max_withdrawal_amount: number; // Static TON value
  min_secondary_market_price: number; // Minimum price for secondary market resale in TON
  fixed_cancel_order_fee: number; // Static TON value for single-person order cancellations
  lock_period: number; // Default lock period in days for collections
}

export interface BotSessionEntity {
  id: string; // User ID
  pendingOrderId?: string;
  echoMode?: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
