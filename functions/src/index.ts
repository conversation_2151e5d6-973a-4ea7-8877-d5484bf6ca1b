import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { beforeUserCreated } from "firebase-functions/v2/identity";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";
import { getConfig } from "./config";
import { log } from "./utils/logger";

if (!admin.apps.length) {
  const config = getConfig();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    log.info("Firebase initialized with service account key", {
      operation: "firebase_init",
      method: "service_account",
    });
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    log.info("Firebase initialized with application default credentials", {
      operation: "firebase_init",
      method: "application_default",
    });
  }
}

admin.firestore().settings({
  ignoreUndefinedProperties: true,
});

const db = admin.firestore();

export const createUserRecord = beforeUserCreated(async (event) => {
  const user = event.data;

  if (!user) {
    log.error("No user data provided in auth event", undefined, {
      operation: "user_creation",
    });
    return;
  }

  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    balance: { sum: 0, locked: 0 },
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    log.info("User record created", {
      userId: user.uid,
      operation: "user_creation",
    });
  } catch (error) {
    log.error("Error creating user record", error, {
      userId: user.uid,
      operation: "user_creation",
    });
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating user record."
    );
  }
});

export const tonTransactionMonitor = onSchedule(
  {
    schedule: "* * * * *",
    timeZone: "UTC",
  },
  async () => {
    try {
      log.monitorLog("TON transaction monitor triggered", {
        monitor: "ton_transaction",
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await monitorTonTransactions();
      log.monitorLog("TON transaction monitor completed successfully", {
        monitor: "ton_transaction",
        status: "completed",
      });
    } catch (error) {
      log.error("TON transaction monitor failed", error, {
        monitor: "ton_transaction",
        status: "failed",
      });
    }
  }
);

export { expiredOrdersMonitor } from "./expired-orders-monitor";

export { limitedCollectionsMonitor } from "./limited-collections-monitor";

export { botHealthCheck } from "./bot-health-check";

export {
  getOrderByIdByBot,
  getUserOrdersByBot,
  sendGiftToRelayerByBot,
  completePurchaseByBot,
} from "./order-functions/bot-order-functions";

export {
  createOrderAsBuyer,
  makePurchaseAsBuyer,
} from "./order-functions/buyer-order-functions";

export {
  createOrderAsSeller,
  makePurchaseAsSeller,
} from "./order-functions/seller-order-functions";

export { cancelUserOrder } from "./order-functions/general-order-functions";

export { withdrawFunds } from "./withdraw-functions";

export { withdrawRevenue } from "./revenue-functions";

export { signInWithTelegram } from "./telegram-auth-functions";

export { changeUserData } from "./user-profile-functions";

export {
  setSecondaryMarketPrice,
  makeSecondaryMarketPurchase,
} from "./order-functions/secondary-market-functions";

export {
  saveUserSessionByBot,
  getUserSessionByBot,
  clearUserSessionByBot,
} from "./bot-session-functions";

export {
  recalculateOrderDeadlines,
  clearOrderDeadlines,
} from "./admin-collection-functions";
