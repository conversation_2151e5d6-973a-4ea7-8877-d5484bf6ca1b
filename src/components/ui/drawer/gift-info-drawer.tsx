'use client';

import { ExternalLink, Gift } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { TELEGRAM_BOT_URL } from '@/constants/core.constants';

import { BaseDrawer } from './base-drawer';
import { DrawerHeader } from './drawer-header';

interface GiftInfoDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: 'seller' | 'buyer';
}

export function GiftInfoDrawer({
  open,
  onOpenChange,
  mode,
}: GiftInfoDrawerProps) {
  const isSeller = mode === 'seller';

  const title = isSeller ? 'Send Gift to Relayer' : 'Claim Your Gift';
  const subtitle = isSeller
    ? 'Follow these steps to send your gift to the relayer'
    : 'Follow these steps to claim your gift from the relayer';

  const instructions = isSeller
    ? [
        <>
          Go to{' '}
          <a
            href={TELEGRAM_BOT_URL}
            target="_blank"
            rel="noopener noreferrer"
            className="text-purple-400 hover:text-purple-300 underline"
          >
            @{process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME}
          </a>
        </>,
        'Press on "My Sell Orders" button',
        'Select your order you want to send',
        <>
          Confirm action, and send this gift to{' '}
          <a
            href="https://t.me/premrelayer"
            target="_blank"
            rel="noopener noreferrer"
            className="text-purple-400 hover:text-purple-300 underline"
          >
            @premrelayer
          </a>
        </>,
      ]
    : [
        <>
          Go to{' '}
          <a
            href={TELEGRAM_BOT_URL}
            target="_blank"
            rel="noopener noreferrer"
            className="text-purple-400 hover:text-purple-300 underline"
          >
            @{process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME}
          </a>
        </>,
        'Press on "My Buy Orders" button',
        'Select your order you want to get',
        <>
          Confirm action, and go to{' '}
          <a
            href="https://t.me/premrelayer"
            target="_blank"
            rel="noopener noreferrer"
            className="text-purple-400 hover:text-purple-300 underline"
          >
            @premrelayer
          </a>{' '}
          to get your gift
        </>,
      ];

  const handleOpenBot = () => {
    window.open(TELEGRAM_BOT_URL, '_blank');
  };

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange}>
      <DrawerHeader
        icon={Gift}
        iconClassName="w-6 h-6 text-purple-400"
        title={title}
        subtitle={subtitle}
      />

      <div className="space-y-4">
        <div className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-4">
          <h4 className="text-sm font-medium text-[#f5f5f5] mb-3">
            Instructions:
          </h4>
          <ol className="space-y-2">
            {instructions.map((instruction, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-purple-500/20 text-purple-400 rounded-full flex items-center justify-center text-xs font-semibold">
                  {index + 1}
                </span>
                <span className="text-sm text-[#708499] leading-relaxed">
                  {instruction}
                </span>
              </li>
            ))}
          </ol>
        </div>

        <div className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-4">
          <img
            src={isSeller ? '/my-sell-orders.gif' : '/my-buy-orders.gif'}
            alt="Step-by-step guide"
            className="w-full max-w-md mx-auto rounded-md"
          />
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]"
          >
            Close
          </Button>
          <Button
            onClick={handleOpenBot}
            className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Open Bot
          </Button>
        </div>
      </div>
    </BaseDrawer>
  );
}
