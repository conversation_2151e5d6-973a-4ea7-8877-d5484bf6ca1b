'use client';

import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import type { OrderEntity } from '@/constants/core.constants';

interface SellPriceDetailsProps {
  order: OrderEntity;
  className?: string;
}

export function SellPriceDetails({
  order,
  className = '',
}: SellPriceDetailsProps) {
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const currentPrice = hasSecondaryPrice
    ? order.secondaryMarketPrice
    : order.price;

  return (
    <div className={`text-center space-y-1 ${className}`}>
      <div className="flex items-center justify-center gap-1">
        <TonPriceDisplay
          amount={currentPrice ?? 0}
          size={24}
          className="text-2xl font-bold text-[#f5f5f5]"
          showUnit
        />
      </div>
      <p className="text-xs text-[#708499]">market fees included</p>
    </div>
  );
}
