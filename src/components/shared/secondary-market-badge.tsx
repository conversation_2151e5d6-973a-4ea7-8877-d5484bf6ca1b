import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface SecondaryMarketBadgeProps {
  className?: string;
}

export function SecondaryMarketBadge({ className }: SecondaryMarketBadgeProps) {
  return (
    <Badge
      variant="secondary"
      className={cn(
        'bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30 text-[10px]',
        className,
      )}
    >
      Resell
    </Badge>
  );
}
