'use client';

import { openTelegramLink, shareURL } from '@telegram-apps/sdk-react';
import { Caption } from '@telegram-apps/telegram-ui';
import { Share } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { makeSecondaryMarketPurchase } from '@/api/orders-api';
import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import {
  type CollectionEntity,
  type OrderEntity,
  OrderStatus,
  UserType,
} from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import { executeMarketplaceOrderAction } from '@/utils/order-action-utils';
import { generateOrderShareLink } from '@/utils/order-deep-link-utils';
import { testOrderShareLink } from '@/utils/telegram-debug';

import {
  OrderDetailsActionButtons,
  OrderDetailsFeesSection,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
} from '../app/(app)/marketplace/order-details-drawer/index';

interface OrderDetailsContentProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
  hideActionButton?: boolean;
  onClose?: () => void;
  showCloseButton?: boolean;
}

export function OrderDetailsContent({
  order,
  collection,
  userType,
  onOrderAction,
  hideActionButton = false,
  onClose,
  showCloseButton = false,
}: OrderDetailsContentProps) {
  const { currentUser } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const effectiveUserType = userType || UserType.BUYER;

  const handleAction = async () => {
    if (!order?.id || !currentUser?.id) return;

    setActionLoading(true);

    let result;

    // Handle secondary market purchase
    if (
      order.status === OrderStatus.PAID &&
      order.secondaryMarketPrice &&
      order.secondaryMarketPrice > 0 &&
      currentUser.id !== order.buyerId &&
      currentUser.id !== order.sellerId
    ) {
      try {
        const purchaseResult = await makeSecondaryMarketPurchase(order.id);
        result = {
          success: purchaseResult.success,
          message: purchaseResult.message,
        };
        if (purchaseResult.success) {
          toast.success(
            purchaseResult.message ||
              'Secondary market purchase completed successfully!',
          );
        } else {
          toast.error(
            purchaseResult.message || 'Secondary market purchase failed',
          );
        }
      } catch (error) {
        console.error('Error making secondary market purchase:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Secondary market purchase failed. Please try again.';
        toast.error(errorMessage);
        result = { success: false, message: errorMessage };
      }
    } else {
      // Handle regular marketplace actions
      let actionUserType: UserType;

      // If order has buyer and current user is not the buyer, they are fulfilling (acting as seller)
      if (order.buyerId && currentUser.id !== order.buyerId) {
        actionUserType = UserType.BUYER;
      }
      // If order has seller and current user is not the seller, they are buying (acting as buyer)
      else if (order.sellerId && currentUser.id !== order.sellerId) {
        actionUserType = UserType.SELLER;
      }
      // Fallback to provided userType
      else {
        actionUserType = effectiveUserType;
      }

      result = await executeMarketplaceOrderAction(order.id, actionUserType);
    }

    if (result.success && onOrderAction) {
      onOrderAction();
    }
    setActionLoading(false);
  };

  const handleShowResellHistory = () => {
    setShowResellHistory(true);
  };

  const handleShare = async () => {
    setIsSharing(true);

    if (!order.id) {
      toast.error('Order ID not available');
      setIsSharing(false);
      return;
    }

    try {
      const shareLink = generateOrderShareLink(order.id);

      testOrderShareLink(order.id);

      if (shareURL.isAvailable()) {
        shareURL(shareLink, 'Check out this order!');
      } else if (openTelegramLink.isAvailable()) {
        openTelegramLink(shareLink);
      } else {
        window.open(shareLink, '_blank');
      }
    } catch (error) {
      console.error('Error sharing order:', error);
      toast.error('Failed to share order');
    } finally {
      setIsSharing(false);
    }
  };

  // TODO: Temporarily hidden - resell tx history feature
  const shouldShowResellHistory = false;
  // order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  // New button logic based on requirements
  const shouldShowButton = () => {
    if (!currentUser?.id) return false;

    // 1) if order status is active and it has buyerid, and current user id !== buyerid, show fulfill button
    if (
      order.status === OrderStatus.ACTIVE &&
      order.buyerId &&
      currentUser.id !== order.buyerId
    ) {
      return true;
    }

    // 2) if order status is active and it has sellerid, and current user id !== sellerid, show buy button
    if (
      order.status === OrderStatus.ACTIVE &&
      order.sellerId &&
      currentUser.id !== order.sellerId
    ) {
      return true;
    }

    // 3) if order status is paid, and secondarymarketprice > 0
    // and not buyerid neither sellerid !== current user id - show buy button
    if (
      order.status === OrderStatus.PAID &&
      order.secondaryMarketPrice &&
      order.secondaryMarketPrice > 0 &&
      currentUser.id !== order.buyerId &&
      currentUser.id !== order.sellerId
    ) {
      return true;
    }

    return false;
  };

  const shouldHideActionButton = hideActionButton || !shouldShowButton();

  const getActionLabel = () => {
    // Secondary market order (paid status with secondary market price > 0)
    if (
      order.status === OrderStatus.PAID &&
      order.secondaryMarketPrice &&
      order.secondaryMarketPrice > 0 &&
      currentUser?.id !== order.buyerId &&
      currentUser?.id !== order.sellerId
    ) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            Buy <span className="font-bold">{order.secondaryMarketPrice}</span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    // Active order with buyer - show fulfill button
    if (
      order.status === OrderStatus.ACTIVE &&
      order.buyerId &&
      currentUser?.id !== order.buyerId
    ) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            Fulfill <span className="font-bold">{order.price}</span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    // Active order with seller - show buy button
    if (
      order.status === OrderStatus.ACTIVE &&
      order.sellerId &&
      currentUser?.id !== order.sellerId
    ) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            Buy <span className="font-bold">{order.price}</span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    // Fallback (shouldn't reach here if shouldShowButton logic is correct)
    return (
      <div className="text-lg flex items-center gap-1">
        <span>
          Action <span className="font-bold">{order.price}</span>
        </span>
        <TonLogo className="-ml-[5px]" size={24} />
      </div>
    );
  };

  const actionLabel = getActionLabel();

  return (
    <div className="space-y-4">
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
      />

      <OrderDetailsHeaderSection
        {...{
          order,
          collection,
        }}
      />

      <div className="flex justify-center">
        <Button
          onClick={handleShare}
          disabled={isSharing}
          variant="outline"
          className="border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
        >
          {isSharing ? (
            <>
              <Share className="w-4 h-4 mr-2 animate-pulse" />
              Opening Telegram...
            </>
          ) : (
            <>
              <Share className="w-4 h-4 mr-1" />
              Share
            </>
          )}
        </Button>
      </div>

      <SellPriceDetails order={order} />

      {collection?.description && (
        <div className="text-center">
          <Caption level="2" weight="3" className="text-[#708499]">
            {collection.description}
          </Caption>
        </div>
      )}

      <OrderDetailsFeesSection order={order} />

      <OrderActors
        buyerId={order.buyerId}
        sellerId={order.sellerId}
        isResellOrder={
          order.status === OrderStatus.PAID &&
          Number(order?.secondaryMarketPrice) > 0
        }
        isOpen={true}
      />

      {!shouldHideActionButton ? (
        <OrderDetailsActionButtons
          primaryAction={{
            label: actionLabel,
            onClick: handleAction,
            loading: actionLoading,
          }}
          secondaryAction={
            shouldShowResellHistory
              ? {
                  label: 'Show Resell History',
                  onClick: handleShowResellHistory,
                }
              : undefined
          }
          shouldShowCloseButton={showCloseButton}
          onClose={onClose}
          actionLoading={actionLoading}
        />
      ) : showCloseButton && onClose ? (
        <div className="space-y-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
          >
            Close
          </Button>
        </div>
      ) : null}

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
