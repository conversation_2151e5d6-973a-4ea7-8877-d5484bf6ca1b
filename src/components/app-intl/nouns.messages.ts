import { defineMessages } from 'react-intl';

export const nounsMessages = defineMessages({
  email: {
    id: 'nouns.email',
    defaultMessage: 'Email',
  },
  password: {
    id: 'nouns.password',
    defaultMessage: 'Password',
  },

  service: {
    id: 'nouns.service',
    defaultMessage: 'Service',
  },

  price: {
    id: 'nouns.price',
    defaultMessage: 'Price',
  },
  from: {
    id: 'nouns.from',
    defaultMessage: 'From',
  },
  name: {
    id: 'nouns.name',
    defaultMessage: 'Name',
  },
  description: {
    id: 'nouns.description',
    defaultMessage: 'Description',
  },
  error: {
    id: 'nouns.error',
    defaultMessage: 'Error',
  },
  confirmation: {
    id: 'nouns.confirmation',
    defaultMessage: 'Confirmation',
  },
});
