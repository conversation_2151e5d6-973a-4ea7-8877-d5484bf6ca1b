import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { getOrderByIdByBot } from "../firebase-service";
import { loadEnvironment } from "../config/env-loader";
import { log } from "./logger";

loadEnvironment();

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  buyer_tg_id?: string;
  seller_tg_id?: string;
  status: string;
  [key: string]: any;
}

const BOT_TOKEN = process.env.BOT_TOKEN;

export const getSentGiftId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  const giftId = businessMessage?.unique_gift?.owned_gift_id;
  return giftId;
};

export const getGiftCollectionId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  const collectionId = businessMessage?.unique_gift?.gift?.gift?.id;
  return collectionId;
};

export const getBusinessConnectionId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.business_connection_id;
};

export const getUniqueGift = (ctx: Context): any => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.unique_gift;
};

export const getOrderById = async (
  orderId: string
): Promise<OrderData | null> => {
  try {
    const result = await getOrderByIdByBot(orderId);
    if (result.success && result.order) {
      return result.order as OrderData;
    }
    return null;
  } catch (error) {
    log.error("Error getting order", error, {
      operation: "business_connection",
      component: "get_order_by_id",
      orderId,
    });
    return null;
  }
};

export const transferGift = async (
  ctx: Context,
  businessConnectionId: string,
  chatId: number,
  owned_gift_id: string
): Promise<void> => {
  try {
    const sendGiftResponse = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          new_owner_chat_id: chatId,
          star_count: 25,
          owned_gift_id,
        }),
      }
    );

    const sendGiftResult = (await sendGiftResponse.json()) as {
      ok: boolean;
      description?: string;
      error_code?: number;
    };

    if (sendGiftResult.ok) {
      log.info(`Successfully sent gift back to user ${chatId}`, {
        operation: "business_connection",
        component: "transfer_gift",
        chatId: String(chatId),
        owned_gift_id,
      });
      await ctx.telegram.sendMessage(
        chatId,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFERRED_SUCCESS
      );
    } else {
      log.error("Failed to transfer gift", sendGiftResult, {
        operation: "business_connection",
        component: "transfer_gift",
        chatId: String(chatId),
        owned_gift_id,
      });
      await ctx.telegram.sendMessage(
        chatId,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_GENERIC_ERROR
      );
    }
  } catch (error) {
    log.error("Error transferring gift", error, {
      operation: "business_connection",
      component: "transfer_gift",
      chatId: String(chatId),
      owned_gift_id,
    });
    await ctx.telegram.sendMessage(
      chatId,
      MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_GENERIC_ERROR
    );
  }
};

export const getBusinessAccountGifts = async (
  businessConnectionId: string
): Promise<any[]> => {
  try {
    const response = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/getBusinessAccountGifts`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          limit: 10,
        }),
      }
    );

    const result = (await response.json()) as {
      ok: boolean;
      result?: {
        gifts: any[];
      };
    };

    if (result.ok && result.result?.gifts) {
      return result.result.gifts;
    }

    return [];
  } catch (error) {
    log.error("Error getting business account gifts", error, {
      operation: "business_connection",
      component: "get_business_account_gifts",
      businessConnectionId,
    });
    return [];
  }
};
