import { Loader2 } from 'lucide-react';

interface LoadingStateProps {
  message?: string;
}

export function LoadingState({
  message = 'Loading your transactions...',
}: LoadingStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <Loader2 className="w-8 h-8 animate-spin text-[#708499]" />
      <p className="text-[#708499] text-sm">{message}</p>
    </div>
  );
}
