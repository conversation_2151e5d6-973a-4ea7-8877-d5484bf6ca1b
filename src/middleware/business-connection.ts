import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { completePurchaseByBot, sendGiftToRelayer } from "../firebase-service";
import { clearUserSession, getUserSession } from "../services/session";
import {
  getBusinessConnectionId,
  getOrderById,
  getSentGiftId,
  getUniqueGift,
  transferGift,
} from "../utils/business-connection-helpers";
import { validateSentGiftWithOrder } from "../utils/gift-validation";
import { TelegramBusinessMessageContext } from "./business-message-types";
import { log } from "../utils/logger";

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    const update = ctx.update as unknown as TelegramBusinessMessageContext;

    if (!update?.business_message) {
      await next();
      return;
    }

    const chat_id = update.business_message.chat.id;

    const userId = update.business_message.from?.id?.toString();
    if (!userId) {
      await next();
      return;
    }

    const session = await getUserSession(userId);

    const pendingOrderId = session?.pendingOrderId;

    if (!pendingOrderId) {
      await ctx.telegram.sendMessage(chat_id, "No pending order found");

      await next();
      return;
    }

    const giftIdToTransfer = getSentGiftId(ctx);

    if (giftIdToTransfer) {
      // Get order with firestore by pendingOrderId, check if collectionId matches
      const order = await getOrderById(pendingOrderId);

      if (!order) {
        await ctx.telegram.sendMessage(
          chat_id,
          MESSAGES.BUSINESS_CONNECTION.ORDER_NOT_FOUND
        );
        await next();
        return;
      }

      // Validate the sent gift with the order
      try {
        const uniqueGift = getUniqueGift(ctx);
        const isValid = await validateSentGiftWithOrder(
          order.collectionId,
          uniqueGift
        );
        if (!isValid) {
          await ctx.telegram.sendMessage(
            chat_id,
            MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
          );
          await next();
          return;
        }
      } catch (error) {
        log.error("Gift validation failed", error, {
          operation: "gift_validation",
          chat_id,
        });
        await ctx.telegram.sendMessage(
          chat_id,
          MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
        );
        await next();
        return;
      }

      // Gift is correct, process it
      await handleGiftToRelayer(ctx, pendingOrderId, giftIdToTransfer, chat_id);

      // Clear user session after successful processing
      await clearUserSession(userId);
      await next();
      return;
    }

    // Logic for buyer to request the gift
    // If no gift to transfer, check if user has an order with status 'gift_sent_to_relayer'
    const existingOrder = await getOrderById(pendingOrderId);

    if (!existingOrder) {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.NO_ORDER_FOR_PROCESSING
      );
      await next();
      return;
    }

    // Validate that current user is the original buyer
    if (existingOrder.buyer_tg_id !== userId) {
      await ctx.telegram.sendMessage(
        chat_id,
        "You are not authorized to request this gift. Only the original buyer can claim the gift."
      );
      await next();
      return;
    }

    const giftToTransferToBuyer = existingOrder.owned_gift_id;

    if (!giftToTransferToBuyer) {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.NO_GIFT_TO_TRANSFER
      );
      await next();
      return;
    }

    const businessConnectionId = getBusinessConnectionId(ctx);

    if (businessConnectionId) {
      await transferGift(
        ctx,
        businessConnectionId,
        chat_id,
        giftToTransferToBuyer
      );
      await completePurchaseByBot(pendingOrderId);
      await clearUserSession(userId);
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_MISSING_INFO
      );
    }

    await next();
  } catch (error) {
    log.error("Error handling update", error, {
      operation: "business_connection_middleware",
    });
    await next();
  }
};

export const handleGiftToRelayer = async (
  ctx: Context,
  orderId: string,
  owned_gift_id: string,
  chat_id: number
) => {
  try {
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.PROCESSING_GIFT
    );

    // Update order status to gift_sent_to_relayer
    const result = await sendGiftToRelayer(orderId, owned_gift_id);

    if (result.success) {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_SENT_SUCCESS
      );
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_PROCESSING_GENERIC_ERROR(orderId)
      );
    }
  } catch (error) {
    log.error("Error handling gift to relayer", error, {
      operation: "handle_gift_to_relayer",
      orderId,
    });

    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.GIFT_PROCESSING_GENERIC_ERROR(orderId)
    );
  }
};
